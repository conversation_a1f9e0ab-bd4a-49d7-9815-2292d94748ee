image: atlassian/default-image:4

definitions:
  services:
    docker:
      memory: 4096

pipelines:
  default:
    - step:
        name: Build and Test
        services:
          - docker
        caches:
          - docker
        script:
          - echo "Building PureStorage Monitor..."
          - export IMAGE_NAME=obs-purestorage-monitor
          - export IMAGE_TAG=${BITBUCKET_COMMIT:0:7}
          - export FULL_IMAGE_NAME=$QUAY_REGISTRY/$QUAY_NAMESPACE/$IMAGE_NAME:$IMAGE_TAG
          
          # Build the Docker image
          - docker build -t $IMAGE_NAME:$IMAGE_TAG .
          - docker tag $IMAGE_NAME:$IMAGE_TAG $FULL_IMAGE_NAME
          - docker tag $IMAGE_NAME:$IMAGE_TAG $QUAY_REGISTRY/$QUAY_NAMESPACE/$IMAGE_NAME:latest
          
          # Basic image test
          - echo "Testing image..."
          - docker run --rm $IMAGE_NAME:$IMAGE_TAG python -c "import src.main; print('Import successful')"

  branches:
    main:
      - step:
          name: Build and Test
          services:
            - docker
          caches:
            - docker
          script:
            - echo "Building PureStorage Monitor for main branch..."
            - export IMAGE_NAME=obs-purestorage-monitor
            - export IMAGE_TAG=${BITBUCKET_COMMIT:0:7}
            - export FULL_IMAGE_NAME=$QUAY_REGISTRY/$QUAY_NAMESPACE/$IMAGE_NAME:$IMAGE_TAG
            
            # Build the Docker image
            - docker build -t $IMAGE_NAME:$IMAGE_TAG .
            - docker tag $IMAGE_NAME:$IMAGE_TAG $FULL_IMAGE_NAME
            - docker tag $IMAGE_NAME:$IMAGE_TAG $QUAY_REGISTRY/$QUAY_NAMESPACE/$IMAGE_NAME:latest
            
            # Basic image test
            - echo "Testing image..."
            - docker run --rm $IMAGE_NAME:$IMAGE_TAG python -c "import src.main; print('Import successful')"
            
            # Save image for next step
            - docker save $FULL_IMAGE_NAME > image.tar
            - docker save $QUAY_REGISTRY/$QUAY_NAMESPACE/$IMAGE_NAME:latest > image-latest.tar
          artifacts:
            - image.tar
            - image-latest.tar

      - step:
          name: Publish to Quay Registry
          services:
            - docker
          script:
            - echo "Publishing to Quay Registry..."
            - export IMAGE_NAME=obs-purestorage-monitor
            - export IMAGE_TAG=${BITBUCKET_COMMIT:0:7}
            - export FULL_IMAGE_NAME=$QUAY_REGISTRY/$QUAY_NAMESPACE/$IMAGE_NAME:$IMAGE_TAG
            
            # Load images
            - docker load < image.tar
            - docker load < image-latest.tar
            
            # Login to Quay
            - echo $QUAY_PASSWORD | docker login -u $QUAY_USERNAME --password-stdin $QUAY_REGISTRY
            
            # Push images
            - docker push $FULL_IMAGE_NAME
            - docker push $QUAY_REGISTRY/$QUAY_NAMESPACE/$IMAGE_NAME:latest
            
            - echo "Successfully pushed:"
            - echo "  - $FULL_IMAGE_NAME"
            - echo "  - $QUAY_REGISTRY/$QUAY_NAMESPACE/$IMAGE_NAME:latest"

      - step:
          name: Deploy to Nomad
          script:
            - echo "Deploying to Nomad..."
            - export IMAGE_NAME=obs-purestorage-monitor
            - export IMAGE_TAG=${BITBUCKET_COMMIT:0:7}
            - export FULL_IMAGE_NAME=$QUAY_REGISTRY/$QUAY_NAMESPACE/$IMAGE_NAME:$IMAGE_TAG
            
            # Install Nomad CLI
            - apt-get update && apt-get install -y curl unzip
            - curl -fsSL https://releases.hashicorp.com/nomad/1.6.1/nomad_1.6.1_linux_amd64.zip -o nomad.zip
            - unzip nomad.zip && chmod +x nomad && mv nomad /usr/local/bin/
            
            # Prepare Nomad job file with current image
            - sed "s|your-registry/purestorage-monitor:latest|$FULL_IMAGE_NAME|g" purestorage-monitor.nomad.hcl > purestorage-monitor-deploy.hcl
            - sed -i "s|pure1:apikey:your-app-id|$PURESTORAGE_APP_ID|g" purestorage-monitor-deploy.hcl
            
            # Deploy to Nomad
            - export NOMAD_ADDR=$NOMAD_ADDRESS
            - export NOMAD_TOKEN=$NOMAD_TOKEN
            - nomad job run purestorage-monitor-deploy.hcl
            
            - echo "Deployment completed!"
            - echo "Image: $FULL_IMAGE_NAME"
