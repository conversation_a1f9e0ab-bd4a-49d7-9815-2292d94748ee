#!/usr/bin/env python3
"""
Simplified PureStorage Health Monitor for Nomad deployment.
Exports Prometheus metrics on port 8080.
"""

import os
import sys
import time
import logging
import schedule
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from prometheus_client import start_http_server, Gauge, Counter, Info, generate_latest
from .purestorage_client import PureStorageClient
from .otel_metrics import OpenTelemetryMetrics

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Prometheus metrics
array_status = Gauge('purestorage_array_status', 'Array status (1=healthy, 0=unhealthy)', ['array_name', 'array_id'])
array_capacity_total = Gauge('purestorage_array_capacity_total_bytes', 'Total array capacity', ['array_name', 'array_id'])
array_capacity_used = Gauge('purestorage_array_capacity_used_bytes', 'Used array capacity', ['array_name', 'array_id'])

controller_status = Gauge('purestorage_controller_status', 'Controller status (1=healthy, 0=unhealthy)', ['array_name', 'controller_name'])
drive_status = Gauge('purestorage_drive_status', 'Drive status (1=healthy, 0=unhealthy)', ['array_name', 'drive_name'])
hardware_status = Gauge('purestorage_hardware_status', 'Hardware status (1=healthy, 0=unhealthy)', ['array_name', 'component_name', 'component_type'])
volume_status = Gauge('purestorage_volume_status', 'Volume status (1=healthy, 0=unhealthy)', ['array_name', 'volume_name'])

alerts_total = Counter('purestorage_alerts_total', 'Total alerts', ['severity', 'state'])
collection_errors = Counter('purestorage_collection_errors_total', 'Collection errors', ['error_type'])

# Info metric for service metadata
service_info = Info('purestorage_monitor_info', 'Service information')

class HealthHandler(BaseHTTPRequestHandler):
    """Simple health check handler."""

    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(b'{"status": "healthy", "service": "purestorage-monitor"}')
        elif self.path == '/metrics':
            self.send_response(200)
            self.send_header('Content-type', 'text/plain; version=0.0.4; charset=utf-8')
            self.end_headers()
            self.wfile.write(generate_latest())
        else:
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        # Suppress default HTTP server logging
        pass

class HealthMonitor:
    def __init__(self):
        # Get configuration from environment variables
        self.app_id = os.getenv('PURESTORAGE_APP_ID')
        self.private_key_path = os.getenv('PURESTORAGE_PRIVATE_KEY_PATH', '/app/private.pem')
        self.public_key_path = os.getenv('PURESTORAGE_PUBLIC_KEY_PATH', '/app/public.pem')
        self.collection_interval = int(os.getenv('COLLECTION_INTERVAL', '300'))  # 5 minutes
        self.metrics_port = int(os.getenv('METRICS_PORT', '8080'))
        
        if not self.app_id:
            logger.error("PURESTORAGE_APP_ID environment variable is required")
            sys.exit(1)
            
        # Initialize PureStorage client
        self.client = PureStorageClient(
            app_id=self.app_id,
            private_key_path=self.private_key_path,
            public_key_path=self.public_key_path
        )

        # Initialize OpenTelemetry metrics
        self.otel_metrics = OpenTelemetryMetrics()

        # Set service info
        service_info.info({
            'version': '1.0.0',
            'app_id': self.app_id,
            'collection_interval': str(self.collection_interval),
            'otel_enabled': str(self.otel_metrics.enabled)
        })
        
    def collect_and_report(self):
        """Collect health data and update Prometheus metrics."""
        try:
            logger.info("Starting health data collection...")
            
            # Authenticate
            if not self.client.authenticate():
                logger.error("Authentication failed")
                collection_errors.labels(error_type='authentication').inc()
                self.otel_metrics.record_collection_error('authentication')
                return
                
            # Get health data
            health_data = self.client.get_health_summary()
            
            # Update metrics
            self._update_array_metrics(health_data.get('arrays', []))
            self._update_controller_metrics(health_data.get('controllers', []))
            self._update_drive_metrics(health_data.get('drives', []))
            self._update_hardware_metrics(health_data.get('hardware', []))
            self._update_volume_metrics(health_data.get('volumes', []))
            self._update_alert_metrics(health_data.get('alerts', []))
            
            logger.info(f"Collection completed: {len(health_data.get('arrays', []))} arrays, "
                       f"{len(health_data.get('controllers', []))} controllers, "
                       f"{len(health_data.get('drives', []))} drives, "
                       f"{len(health_data.get('hardware', []))} hardware, "
                       f"{len(health_data.get('volumes', []))} volumes")
                       
        except Exception as e:
            logger.error(f"Collection failed: {e}")
            collection_errors.labels(error_type='collection').inc()
            self.otel_metrics.record_collection_error('collection')
    
    def _update_array_metrics(self, arrays):
        """Update array metrics."""
        for array in arrays:
            array_name = array.get('name', 'unknown')
            array_id = array.get('id', 'unknown')
            status = 1 if array.get('status', 'healthy') == 'healthy' else 0
            
            array_status.labels(array_name=array_name, array_id=array_id).set(status)
            self.otel_metrics.record_array_status(array_name, array_id, status)

            # Capacity metrics
            space = array.get('space', {})
            if space:
                total = space.get('total_capacity', 0)
                used = space.get('used_capacity', 0)
                if total > 0:
                    array_capacity_total.labels(array_name=array_name, array_id=array_id).set(total)
                if used > 0:
                    array_capacity_used.labels(array_name=array_name, array_id=array_id).set(used)
                self.otel_metrics.record_array_capacity(array_name, array_id, total, used)
    
    def _update_controller_metrics(self, controllers):
        """Update controller metrics."""
        for controller in controllers:
            array_name = controller.get('array', {}).get('name', 'unknown')
            controller_name = controller.get('name', 'unknown')
            status = 1 if controller.get('status', 'healthy') == 'healthy' else 0
            
            controller_status.labels(array_name=array_name, controller_name=controller_name).set(status)
            self.otel_metrics.record_controller_status(array_name, controller_name, status)
    
    def _update_drive_metrics(self, drives):
        """Update drive metrics."""
        for drive in drives:
            array_name = drive.get('array', {}).get('name', 'unknown')
            drive_name = drive.get('name', 'unknown')
            status = 1 if drive.get('status', 'healthy') == 'healthy' else 0
            
            drive_status.labels(array_name=array_name, drive_name=drive_name).set(status)
            self.otel_metrics.record_drive_status(array_name, drive_name, status)
    
    def _update_hardware_metrics(self, hardware):
        """Update hardware metrics."""
        for hw in hardware:
            array_name = hw.get('array', {}).get('name', 'unknown')
            component_name = hw.get('name', 'unknown')
            component_type = hw.get('type', 'unknown')
            status = 1 if hw.get('status', 'healthy') == 'healthy' else 0
            
            hardware_status.labels(
                array_name=array_name,
                component_name=component_name,
                component_type=component_type
            ).set(status)
            self.otel_metrics.record_hardware_status(array_name, component_name, component_type, status)
    
    def _update_volume_metrics(self, volumes):
        """Update volume metrics."""
        for volume in volumes:
            array_name = volume.get('array', {}).get('name', 'unknown')
            volume_name = volume.get('name', 'unknown')
            status = 1 if volume.get('status', 'healthy') == 'healthy' else 0
            
            volume_status.labels(array_name=array_name, volume_name=volume_name).set(status)
            self.otel_metrics.record_volume_status(array_name, volume_name, status)
    
    def _update_alert_metrics(self, alerts):
        """Update alert metrics."""
        # Count alerts by severity and state
        alert_counts = {}
        for alert in alerts:
            severity = alert.get('severity', 'unknown')
            state = alert.get('state', 'unknown')
            key = (severity, state)
            alert_counts[key] = alert_counts.get(key, 0) + 1
        
        # Update counters
        for (severity, state), count in alert_counts.items():
            alerts_total.labels(severity=severity, state=state).inc(count)
            self.otel_metrics.record_alerts(severity, state, count)
    
    def run(self):
        """Run the health monitor."""
        logger.info(f"Starting PureStorage Health Monitor on port {self.metrics_port}")

        # Start HTTP server with health and metrics endpoints
        server = HTTPServer(('', self.metrics_port), HealthHandler)
        server_thread = threading.Thread(target=server.serve_forever, daemon=True)
        server_thread.start()
        logger.info(f"HTTP server started on port {self.metrics_port}")

        # Schedule collection
        schedule.every(self.collection_interval).seconds.do(self.collect_and_report)

        # Run initial collection
        self.collect_and_report()

        # Main loop
        try:
            while True:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Shutting down...")
            self.otel_metrics.shutdown()
            server.shutdown()

def main():
    """Main entry point."""
    monitor = HealthMonitor()
    monitor.run()

if __name__ == '__main__':
    main()
