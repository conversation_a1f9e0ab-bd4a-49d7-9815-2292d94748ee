job "purestorage-monitor" {
  datacenters = ["dc1"]
  type        = "service"
  
  # Constraint to run on specific nodes if needed
  # constraint {
  #   attribute = "${node.class}"
  #   value     = "monitoring"
  # }

  group "monitor" {
    count = 1

    # Restart policy
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "fail"
    }

    # Update strategy
    update {
      max_parallel     = 1
      min_healthy_time = "30s"
      healthy_deadline = "3m"
      auto_revert      = true
    }

    # Network configuration
    network {
      port "metrics" {
        static = 8080
      }
    }

    # Service registration for Prometheus discovery
    service {
      name = "purestorage-monitor"
      port = "metrics"
      
      tags = [
        "monitoring",
        "purestorage",
        "prometheus"
      ]

      check {
        type     = "http"
        path     = "/metrics"
        interval = "30s"
        timeout  = "10s"
      }
    }

    task "monitor" {
      driver = "docker"

      config {
        image = "your-registry/purestorage-monitor:latest"
        ports = ["metrics"]
        
        # Mount SSL certificates
        volumes = [
          "/opt/purestorage/certs:/app/certs:ro"
        ]
      }

      # Environment variables for configuration
      env {
        PURESTORAGE_APP_ID           = "pure1:apikey:your-app-id"
        PURESTORAGE_PRIVATE_KEY_PATH = "/app/certs/private.pem"
        PURESTORAGE_PUBLIC_KEY_PATH  = "/app/certs/public.pem"
        COLLECTION_INTERVAL          = "300"  # 5 minutes
        METRICS_PORT                 = "8080"
      }

      # Resource requirements
      resources {
        cpu    = 100   # MHz
        memory = 128   # MB
      }

      # Logs configuration
      logs {
        max_files     = 3
        max_file_size = 10
      }
    }
  }
}
