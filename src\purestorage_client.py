"""
Simplified PureStorage API client for health monitoring.
"""

import os
import json
import time
import logging
import requests
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
import jwt

logger = logging.getLogger(__name__)

class PureStorageClient:
    def __init__(self, app_id, private_key_path, public_key_path):
        self.app_id = app_id
        self.private_key_path = private_key_path
        self.public_key_path = public_key_path
        self.api_base_url = "https://api.pure1.purestorage.com"
        self.access_token = None
        self.token_expires_at = None
        
    def _load_private_key(self):
        """Load private key from file."""
        try:
            with open(self.private_key_path, 'rb') as key_file:
                private_key = serialization.load_pem_private_key(
                    key_file.read(),
                    password=None
                )
            return private_key
        except Exception as e:
            logger.error(f"Failed to load private key: {e}")
            return None
    
    def _generate_jwt_token(self):
        """Generate JWT token for authentication."""
        private_key = self._load_private_key()
        if not private_key:
            return None
            
        # JWT payload
        now = datetime.utcnow()
        payload = {
            'iss': self.app_id,
            'sub': self.app_id,
            'aud': 'pure1',
            'iat': int(now.timestamp()),
            'exp': int((now + timedelta(minutes=10)).timestamp())
        }
        
        # Generate JWT
        try:
            token = jwt.encode(payload, private_key, algorithm='RS256')
            logger.info(f"Generated JWT token expiring at {payload['exp']}")
            return token
        except Exception as e:
            logger.error(f"Failed to generate JWT token: {e}")
            return None
    
    def _exchange_token(self, jwt_token):
        """Exchange JWT token for access token."""
        url = f"{self.api_base_url}/oauth2/1.0/token"
        
        data = {
            'grant_type': 'urn:ietf:params:oauth:grant-type:token-exchange',
            'subject_token': jwt_token,
            'subject_token_type': 'urn:ietf:params:oauth:token-type:jwt'
        }
        
        try:
            response = requests.post(url, data=data, timeout=30)
            response.raise_for_status()
            
            token_data = response.json()
            self.access_token = token_data['access_token']
            expires_in = token_data.get('expires_in', 3600)
            self.token_expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
            
            logger.info(f"Token exchange successful. Token expires at {self.token_expires_at}")
            return True
            
        except Exception as e:
            logger.error(f"Token exchange failed: {e}")
            return False
    
    def authenticate(self):
        """Authenticate with PureStorage API."""
        # Check if current token is still valid
        if (self.access_token and self.token_expires_at and 
            datetime.utcnow() < self.token_expires_at - timedelta(minutes=5)):
            return True
        
        # Generate new JWT token
        jwt_token = self._generate_jwt_token()
        if not jwt_token:
            return False
        
        # Exchange for access token
        return self._exchange_token(jwt_token)
    
    def _make_api_request(self, endpoint, params=None):
        """Make authenticated API request."""
        if not self.access_token:
            logger.error("No access token available")
            return None
        
        url = f"{self.api_base_url}{endpoint}"
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"API request failed for {endpoint}: {e}")
            return None
    
    def get_arrays(self):
        """Get array information."""
        data = self._make_api_request('/api/1.2/arrays', {'limit': 100})
        if data:
            arrays = data.get('items', [])
            logger.info(f"Retrieved {len(arrays)} arrays")
            
            # Enrich with capacity data
            array_ids = [array['id'] for array in arrays]
            capacity_data = self._get_array_capacity_metrics(array_ids)
            
            # Add capacity to arrays
            for array in arrays:
                array_id = array['id']
                if array_id in capacity_data:
                    array['space'] = capacity_data[array_id]
            
            return arrays
        return []
    
    def _get_array_capacity_metrics(self, array_ids):
        """Get array capacity metrics from metrics endpoint."""
        if not array_ids:
            return {}
        
        # Query metrics for capacity data
        params = {
            'names': 'array_total_capacity,array_unique_space',
            'resource_ids': ','.join(array_ids),
            'limit': 1000
        }
        
        data = self._make_api_request('/api/1.2/metrics/history', params)
        capacity_data = {}
        
        if data and 'items' in data:
            for item in data['items']:
                resource_id = item.get('resource_id')
                metric_name = item.get('name')
                
                if resource_id and metric_name and item.get('data'):
                    if resource_id not in capacity_data:
                        capacity_data[resource_id] = {}
                    
                    # Get latest value
                    latest_value = item['data'][-1][1] if item['data'] else 0
                    
                    if metric_name == 'array_total_capacity':
                        capacity_data[resource_id]['total_capacity'] = latest_value
                    elif metric_name == 'array_unique_space':
                        capacity_data[resource_id]['used_capacity'] = latest_value
        
        return capacity_data
    
    def get_alerts(self):
        """Get alerts."""
        alerts = []
        for severity in ['critical', 'warning']:
            data = self._make_api_request('/api/1.2/alerts', {
                'limit': 500,
                'severity': severity,
                'state': 'open'
            })
            if data:
                alerts.extend(data.get('items', []))
        
        logger.info(f"Retrieved {len(alerts)} alerts")
        return alerts
    
    def get_controllers(self):
        """Get controller information."""
        data = self._make_api_request('/api/1.2/controllers', {'limit': 200})
        if data:
            controllers = data.get('items', [])
            logger.info(f"Retrieved {len(controllers)} controllers")
            return controllers
        return []
    
    def get_drives(self):
        """Get drive information."""
        data = self._make_api_request('/api/1.2/drives', {'limit': 1000})
        if data:
            drives = data.get('items', [])
            logger.info(f"Retrieved {len(drives)} drives")
            return drives
        return []
    
    def get_hardware(self):
        """Get hardware information."""
        data = self._make_api_request('/api/1.2/hardware', {'limit': 500})
        if data:
            hardware = data.get('items', [])
            logger.info(f"Retrieved {len(hardware)} hardware components")
            return hardware
        return []
    
    def get_volumes(self):
        """Get volume information."""
        data = self._make_api_request('/api/1.2/volumes', {'limit': 1500})
        if data:
            volumes = data.get('items', [])
            logger.info(f"Retrieved {len(volumes)} volumes")
            return volumes
        return []
    
    def get_health_summary(self):
        """Get comprehensive health summary."""
        return {
            'arrays': self.get_arrays(),
            'alerts': self.get_alerts(),
            'controllers': self.get_controllers(),
            'drives': self.get_drives(),
            'hardware': self.get_hardware(),
            'volumes': self.get_volumes()
        }
