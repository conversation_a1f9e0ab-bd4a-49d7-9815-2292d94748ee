# Simplified PureStorage Health Monitor for Nomad

A lightweight PureStorage health monitor designed for Nomad deployment with Prometheus metrics export.

## Features

- **Environment Variable Configuration** - No config files needed
- **Prometheus Metrics** - Direct metrics export on port 8080
- **Minimal Dependencies** - Only essential packages
- **Nomad Ready** - Simple .hcl job specification included
- **Health Checks** - Built-in health endpoint for monitoring

## Quick Start

### 1. Build the Image

```bash
# Set your registry
export REGISTRY=your-registry.com

# Build and optionally push
./build-simple.sh latest push
```

### 2. Prepare Certificates

Place your PureStorage SSL certificates on your Nomad nodes:

```bash
# On each Nomad node
sudo mkdir -p /opt/purestorage/certs
sudo cp private.pem /opt/purestorage/certs/
sudo cp public.pem /opt/purestorage/certs/
sudo chmod 600 /opt/purestorage/certs/*.pem
```

### 3. Update Nomad Job

Edit `purestorage-monitor.nomad.hcl`:

```hcl
# Update the image name
image = "your-registry.com/purestorage-monitor:latest"

# Update environment variables
env {
  PURESTORAGE_APP_ID = "pure1:apikey:your-actual-app-id"
  # ... other variables
}
```

### 4. Deploy to Nomad

```bash
nomad job run purestorage-monitor.nomad.hcl
```

## Configuration

All configuration is done via environment variables:

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `PURESTORAGE_APP_ID` | Yes | - | Your PureStorage App ID |
| `PURESTORAGE_PRIVATE_KEY_PATH` | No | `/app/certs/private.pem` | Path to private key |
| `PURESTORAGE_PUBLIC_KEY_PATH` | No | `/app/certs/public.pem` | Path to public key |
| `COLLECTION_INTERVAL` | No | `300` | Collection interval in seconds |
| `METRICS_PORT` | No | `8080` | Port for Prometheus metrics |

## Metrics

The following Prometheus metrics are exported on `/metrics`:

### Status Metrics (Gauges)
- `purestorage_array_status{array_name, array_id}` - Array health (1=healthy, 0=unhealthy)
- `purestorage_controller_status{array_name, controller_name}` - Controller health
- `purestorage_drive_status{array_name, drive_name}` - Drive health
- `purestorage_hardware_status{array_name, component_name, component_type}` - Hardware health
- `purestorage_volume_status{array_name, volume_name}` - Volume health

### Capacity Metrics (Gauges)
- `purestorage_array_capacity_total_bytes{array_name, array_id}` - Total capacity
- `purestorage_array_capacity_used_bytes{array_name, array_id}` - Used capacity

### Alert Metrics (Counters)
- `purestorage_alerts_total{severity, state}` - Alert counts

### Service Metrics
- `purestorage_collection_errors_total{error_type}` - Collection errors
- `purestorage_monitor_info` - Service information

## Prometheus Configuration

Add to your Prometheus configuration:

```yaml
scrape_configs:
  - job_name: 'purestorage-monitor'
    consul_sd_configs:
      - server: 'consul.service.consul:8500'
        services: ['purestorage-monitor']
    metrics_path: /metrics
    scrape_interval: 30s
```

## Monitoring

### Health Check

The service provides a health endpoint:

```bash
curl http://localhost:8080/health
```

### Logs

View logs via Nomad:

```bash
nomad alloc logs -f <allocation-id> monitor
```

### Metrics

View metrics:

```bash
curl http://localhost:8080/metrics
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify `PURESTORAGE_APP_ID` is correct
   - Check SSL certificate paths and permissions
   - Ensure certificates are valid

2. **Network Issues**
   - Verify connectivity to `api.pure1.purestorage.com`
   - Check firewall rules for outbound HTTPS

3. **Resource Issues**
   - Monitor CPU and memory usage
   - Adjust resource limits in Nomad job if needed

### Debug Mode

To enable debug logging, add to the Nomad job:

```hcl
env {
  LOG_LEVEL = "DEBUG"
}
```

## Differences from Full Version

This simplified version removes:
- YAML configuration files
- Complex OTLP/OpenTelemetry setup
- Docker Compose complexity
- Multiple reporter backends
- Advanced scheduling features

## Migration from Full Version

To migrate from the full version:

1. Extract your configuration values from `config.yaml`
2. Set equivalent environment variables in the Nomad job
3. Update your Prometheus configuration to scrape the new service
4. Deploy the simplified version

The metrics names and labels remain the same, so existing dashboards should continue to work.
