"""
OpenTelemetry metrics configuration and export for PureStorage Health Monitor.
"""

import os
import logging
from typing import Optional, Dict, Any
from opentelemetry import metrics
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
from opentelemetry.sdk.resources import Resource

logger = logging.getLogger(__name__)

class OpenTelemetryMetrics:
    """OpenTelemetry metrics manager for PureStorage health monitoring."""
    
    def __init__(self):
        self.meter_provider: Optional[MeterProvider] = None
        self.meter = None
        self.enabled = False
        
        # Metric instruments
        self.array_status_gauge = None
        self.array_capacity_total_gauge = None
        self.array_capacity_used_gauge = None
        self.controller_status_gauge = None
        self.drive_status_gauge = None
        self.hardware_status_gauge = None
        self.volume_status_gauge = None
        self.alerts_counter = None
        self.collection_errors_counter = None
        
        # Configuration from environment
        self.otlp_endpoint = os.getenv('OTEL_EXPORTER_OTLP_ENDPOINT')
        self.otlp_headers = self._parse_headers(os.getenv('OTEL_EXPORTER_OTLP_HEADERS', ''))
        self.service_name = os.getenv('OTEL_SERVICE_NAME', 'purestorage-monitor')
        self.service_version = os.getenv('OTEL_SERVICE_VERSION', '1.0.0')
        self.export_interval = int(os.getenv('OTEL_METRIC_EXPORT_INTERVAL', '60'))
        
        # Initialize if endpoint is configured
        if self.otlp_endpoint:
            self._initialize()
        else:
            logger.info("OpenTelemetry disabled: OTEL_EXPORTER_OTLP_ENDPOINT not configured")
    
    def _parse_headers(self, headers_str: str) -> Dict[str, str]:
        """Parse OTLP headers from environment variable."""
        headers = {}
        if headers_str:
            for header in headers_str.split(','):
                if '=' in header:
                    key, value = header.strip().split('=', 1)
                    headers[key] = value
        return headers
    
    def _initialize(self):
        """Initialize OpenTelemetry metrics."""
        try:
            # Create resource with service information
            resource = Resource.create({
                "service.name": self.service_name,
                "service.version": self.service_version,
                "service.instance.id": os.getenv('HOSTNAME', 'unknown'),
            })
            
            # Create OTLP exporter
            otlp_exporter = OTLPMetricExporter(
                endpoint=self.otlp_endpoint,
                headers=self.otlp_headers,
                timeout=30
            )
            
            # Create metric reader with periodic export
            metric_reader = PeriodicExportingMetricReader(
                exporter=otlp_exporter,
                export_interval_millis=self.export_interval * 1000
            )
            
            # Create meter provider
            self.meter_provider = MeterProvider(
                resource=resource,
                metric_readers=[metric_reader]
            )
            
            # Set global meter provider
            metrics.set_meter_provider(self.meter_provider)
            
            # Get meter
            self.meter = metrics.get_meter(
                name="purestorage.monitor",
                version=self.service_version
            )
            
            # Create metric instruments
            self._create_instruments()
            
            self.enabled = True
            logger.info(f"OpenTelemetry metrics initialized with endpoint: {self.otlp_endpoint}")
            
        except Exception as e:
            logger.error(f"Failed to initialize OpenTelemetry metrics: {e}")
            self.enabled = False
    
    def _create_instruments(self):
        """Create OpenTelemetry metric instruments."""
        if not self.meter:
            return
        
        # Status gauges
        self.array_status_gauge = self.meter.create_gauge(
            name="purestorage.array.status",
            description="Array health status (1=healthy, 0=unhealthy)",
            unit="1"
        )
        
        self.controller_status_gauge = self.meter.create_gauge(
            name="purestorage.controller.status",
            description="Controller health status (1=healthy, 0=unhealthy)",
            unit="1"
        )
        
        self.drive_status_gauge = self.meter.create_gauge(
            name="purestorage.drive.status",
            description="Drive health status (1=healthy, 0=unhealthy)",
            unit="1"
        )
        
        self.hardware_status_gauge = self.meter.create_gauge(
            name="purestorage.hardware.status",
            description="Hardware component health status (1=healthy, 0=unhealthy)",
            unit="1"
        )
        
        self.volume_status_gauge = self.meter.create_gauge(
            name="purestorage.volume.status",
            description="Volume health status (1=healthy, 0=unhealthy)",
            unit="1"
        )
        
        # Capacity gauges
        self.array_capacity_total_gauge = self.meter.create_gauge(
            name="purestorage.array.capacity.total",
            description="Total array capacity in bytes",
            unit="By"
        )
        
        self.array_capacity_used_gauge = self.meter.create_gauge(
            name="purestorage.array.capacity.used",
            description="Used array capacity in bytes",
            unit="By"
        )
        
        # Counters
        self.alerts_counter = self.meter.create_counter(
            name="purestorage.alerts.total",
            description="Total number of alerts",
            unit="1"
        )
        
        self.collection_errors_counter = self.meter.create_counter(
            name="purestorage.collection.errors.total",
            description="Total number of collection errors",
            unit="1"
        )
    
    def record_array_status(self, array_name: str, array_id: str, status: int):
        """Record array status metric."""
        if self.enabled and self.array_status_gauge:
            self.array_status_gauge.set(
                status,
                {"array.name": array_name, "array.id": array_id}
            )
    
    def record_array_capacity(self, array_name: str, array_id: str, total: int, used: int):
        """Record array capacity metrics."""
        if not self.enabled:
            return
        
        if self.array_capacity_total_gauge and total > 0:
            self.array_capacity_total_gauge.set(
                total,
                {"array.name": array_name, "array.id": array_id}
            )
        
        if self.array_capacity_used_gauge and used > 0:
            self.array_capacity_used_gauge.set(
                used,
                {"array.name": array_name, "array.id": array_id}
            )
    
    def record_controller_status(self, array_name: str, controller_name: str, status: int):
        """Record controller status metric."""
        if self.enabled and self.controller_status_gauge:
            self.controller_status_gauge.set(
                status,
                {"array.name": array_name, "controller.name": controller_name}
            )
    
    def record_drive_status(self, array_name: str, drive_name: str, status: int):
        """Record drive status metric."""
        if self.enabled and self.drive_status_gauge:
            self.drive_status_gauge.set(
                status,
                {"array.name": array_name, "drive.name": drive_name}
            )
    
    def record_hardware_status(self, array_name: str, component_name: str, component_type: str, status: int):
        """Record hardware status metric."""
        if self.enabled and self.hardware_status_gauge:
            self.hardware_status_gauge.set(
                status,
                {
                    "array.name": array_name,
                    "component.name": component_name,
                    "component.type": component_type
                }
            )
    
    def record_volume_status(self, array_name: str, volume_name: str, status: int):
        """Record volume status metric."""
        if self.enabled and self.volume_status_gauge:
            self.volume_status_gauge.set(
                status,
                {"array.name": array_name, "volume.name": volume_name}
            )
    
    def record_alerts(self, severity: str, state: str, count: int = 1):
        """Record alert metrics."""
        if self.enabled and self.alerts_counter:
            self.alerts_counter.add(
                count,
                {"severity": severity, "state": state}
            )
    
    def record_collection_error(self, error_type: str, count: int = 1):
        """Record collection error metrics."""
        if self.enabled and self.collection_errors_counter:
            self.collection_errors_counter.add(
                count,
                {"error.type": error_type}
            )
    
    def shutdown(self):
        """Shutdown OpenTelemetry metrics."""
        if self.meter_provider:
            try:
                self.meter_provider.shutdown()
                logger.info("OpenTelemetry metrics shutdown completed")
            except Exception as e:
                logger.error(f"Error during OpenTelemetry shutdown: {e}")
